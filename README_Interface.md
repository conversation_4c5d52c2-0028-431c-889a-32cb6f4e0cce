# 🔬 Interface Graphique de Traitement d'Images Médicales

Cette interface graphique vous permet d'utiliser facilement tous vos scripts de traitement d'images médicales via une interface conviviale.

## 🚀 Lancement

```bash
python interface_graphique.py
```

## 📋 Scripts Disponibles

### 🔍 Vérifier la forme d'un fichier NIfTI (`check_shape.py`)
- **Description**: Affiche les dimensions et informations d'un fichier .nii.gz
- **Paramètres**: 
  - Fichier NIfTI d'entrée
- **Utilisation**: Utile pour vérifier la structure d'un volume avant traitement

### 📦 Convertir dataset 2D vers 3D (`convertir_dataset_2d_to_3d.py`)
- **Description**: Convertit des images PNG 2D en volumes 3D NIfTI pour l'entraînement nnU-Net
- **Paramètres**:
  - Dossier images (PNG)
  - Dossier labels (PNG)
  - Dossier de sortie
  - Nom du dataset
  - Nombre de slices par volume (défaut: 30)
- **Utilisation**: Préparation de données d'entraînement pour nnU-Net

### 🔄 Convertir inférence 2D vers 3D (`convertir_inference_2d_to_3d.py`)
- **Description**: Convertit des images PNG 2D en volumes 3D pour l'inférence
- **Paramètres**:
  - Dossier images (PNG)
  - Dossier de sortie
  - Nombre de slices par volume (défaut: 30)
- **Utilisation**: Préparation de données pour l'inférence avec nnU-Net

### 🔀 Transposer un fichier NIfTI (`transpose_nifti.py`)
- **Description**: Change l'orientation des axes d'un volume NIfTI
- **Paramètres**:
  - Fichier d'entrée (.nii.gz)
  - Fichier de sortie (.nii.gz)
- **Utilisation**: Correction d'orientation des volumes

### 👁️ Visualiser un volume NIfTI (`view_nifti_volume.py`)
- **Description**: Affiche les slices d'un volume NIfTI avec colormap personnalisée
- **Paramètres**:
  - Fichier NIfTI
- **Utilisation**: Visualisation de résultats de segmentation

### 🎨 Visualiser 10 masques (`visualise_10_mask.py`)
- **Description**: Affiche plusieurs masques PNG avec couleurs personnalisées
- **Paramètres**:
  - Dossier masques
  - Nombre d'images à afficher (défaut: 10)
- **Utilisation**: Visualisation rapide de plusieurs masques

### 📊 Analyser les labels (`visualize_labels.py`)
- **Description**: Analyse les valeurs uniques dans les images de labels
- **Paramètres**:
  - Dossier labels
- **Utilisation**: Vérification de la cohérence des labels

### 🖼️ Visualiser un masque (`visualize_mask.py`)
- **Description**: Visualise un masque PNG avec normalisation et mapping des valeurs
- **Paramètres**:
  - Fichier image (PNG)
- **Utilisation**: Analyse détaillée d'un masque spécifique

## 💡 Guide d'Utilisation

1. **Lancer l'interface**: Exécutez `python interface_graphique.py`

2. **Sélectionner un script**: Choisissez le script désiré dans la liste déroulante

3. **Configurer les paramètres**: 
   - Utilisez les boutons "📁 Fichier" ou "📂 Dossier" pour sélectionner les chemins
   - Ou tapez directement les chemins dans les champs de texte
   - Ajustez les paramètres numériques si nécessaire

4. **Exécuter**: Cliquez sur "🚀 Exécuter"

5. **Suivre l'exécution**: La sortie du script s'affiche en temps réel dans la zone de texte

6. **Aide**: Cliquez sur "❓ Aide" pour plus d'informations

## ⚠️ Notes Importantes

- **Chemins**: Utilisez des chemins absolus ou relatifs au répertoire de travail
- **Formats supportés**: PNG pour les images 2D, .nii.gz pour les volumes 3D
- **Exécution**: Les scripts s'exécutent en arrière-plan, l'interface reste responsive
- **Nettoyage**: Les fichiers temporaires sont automatiquement supprimés après exécution

## 🔧 Dépendances

Assurez-vous d'avoir installé les bibliothèques suivantes :

```bash
pip install nibabel numpy matplotlib pillow natsort opencv-python tkinter
```

## 🐛 Dépannage

- **Erreur de chemin**: Vérifiez que les chemins existent et sont accessibles
- **Erreur de format**: Assurez-vous que les fichiers sont dans le bon format
- **Erreur de permissions**: Vérifiez les droits d'écriture dans les dossiers de sortie

## 📝 Fonctionnalités

- ✅ Interface graphique intuitive
- ✅ Sélection de fichiers/dossiers via dialogue
- ✅ Exécution en temps réel avec affichage de la sortie
- ✅ Gestion d'erreurs et validation des paramètres
- ✅ Aide intégrée
- ✅ Support de tous vos scripts existants
