"""
Interface graphique simplifiée pour utiliser les scripts de traitement d'images
Auteur: Gabriel Forest
Date: 2025-06-18
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import subprocess
import sys
import threading
import queue
import time

class SimpleScriptGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Interface de Traitement d'Images")
        self.root.geometry("900x700")
        
        # Queue pour la communication thread-safe
        self.output_queue = queue.Queue()
        
        # Variables pour stocker les chemins
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.labels_path = tk.StringVar()
        self.file_path = tk.StringVar()
        
        self.setup_ui()
        self.check_queue()
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="🔬 Interface de Traitement d'Images", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Sélection du script
        ttk.Label(main_frame, text="Choisir le script:", font=('Arial', 12, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.script_var = tk.StringVar()
        self.script_combo = ttk.Combobox(main_frame, textvariable=self.script_var, width=50, state="readonly")
        self.script_combo['values'] = [
            "Vérifier la forme d'un fichier NIfTI (check_shape.py)",
            "Convertir dataset 2D vers 3D (convertir_dataset_2d_to_3d.py)",
            "Convertir inférence 2D vers 3D (convertir_inference_2d_to_3d.py)",
            "Transposer un fichier NIfTI (transpose_nifti.py)",
            "Visualiser un volume NIfTI (view_nifti_volume.py)",
            "Visualiser 10 masques (visualise_10_mask.py)",
            "Analyser les labels (visualize_labels.py)",
            "Visualiser un masque (visualize_mask.py)"
        ]
        self.script_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.script_combo.bind('<<ComboboxSelected>>', self.on_script_change)
        
        # Frame pour les paramètres
        self.params_frame = ttk.LabelFrame(main_frame, text="Paramètres", padding="10")
        self.params_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        self.params_frame.columnconfigure(1, weight=1)
        
        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=3, pady=10)
        
        # Bouton exécuter
        self.execute_btn = ttk.Button(buttons_frame, text="Exécuter", command=self.execute_script)
        self.execute_btn.pack(side=tk.LEFT, padx=5)
        
        # Zone de sortie
        output_frame = ttk.LabelFrame(main_frame, text="Sortie du script", padding="5")
        output_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
        
        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=80)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        main_frame.rowconfigure(4, weight=1)
        
    def check_queue(self):
        """Vérifie la queue pour les messages des threads"""
        try:
            while True:
                message = self.output_queue.get_nowait()
                self.output_text.insert(tk.END, message)
                self.output_text.see(tk.END)
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.check_queue)
            
    def clear_params_frame(self):
        """Efface tous les widgets du frame des paramètres"""
        for widget in self.params_frame.winfo_children():
            widget.destroy()
            
    def on_script_change(self, event=None):
        """Appelé quand l'utilisateur change de script"""
        self.clear_params_frame()
        script_name = self.script_var.get()
        
        if "check_shape" in script_name:
            self.setup_file_selector("Fichier NIfTI:", self.file_path)
        elif "dataset_2d_to_3d" in script_name:
            self.setup_dataset_2d_to_3d_params()
        elif "inference_2d_to_3d" in script_name:
            self.setup_inference_2d_to_3d_params()
        elif "transpose_nifti" in script_name:
            self.setup_transpose_params()
        elif "view_nifti_volume" in script_name:
            self.setup_file_selector("Fichier NIfTI:", self.file_path)
        elif "visualise_10_mask" in script_name:
            self.setup_visualise_masks_params()
        elif "visualize_labels" in script_name:
            self.setup_folder_selector("Dossier labels:", self.input_path)
        elif "visualize_mask" in script_name:
            self.setup_file_selector("Fichier image:", self.file_path)
            
    def setup_file_selector(self, label_text, var):
        """Crée un sélecteur de fichier"""
        ttk.Label(self.params_frame, text=label_text).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=var, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_file(var)).grid(row=0, column=2, pady=2)
        
    def setup_folder_selector(self, label_text, var):
        """Crée un sélecteur de dossier"""
        ttk.Label(self.params_frame, text=label_text).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=var, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_directory(var)).grid(row=0, column=2, pady=2)
        
    def setup_dataset_2d_to_3d_params(self):
        """Configuration pour convertir_dataset_2d_to_3d.py"""
        ttk.Label(self.params_frame, text="Dossier images:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.input_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_directory(self.input_path)).grid(row=0, column=2, pady=2)
        
        ttk.Label(self.params_frame, text="Dossier labels:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.labels_path, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_directory(self.labels_path)).grid(row=1, column=2, pady=2)
        
        ttk.Label(self.params_frame, text="Dossier sortie:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.output_path, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_directory(self.output_path)).grid(row=2, column=2, pady=2)
        
        ttk.Label(self.params_frame, text="Nom du dataset:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.dataset_name = tk.StringVar(value="Dataset_3d")
        ttk.Entry(self.params_frame, textvariable=self.dataset_name, width=30).grid(row=3, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(self.params_frame, text="Slices par volume:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.slices_per_volume = tk.StringVar(value="30")
        ttk.Entry(self.params_frame, textvariable=self.slices_per_volume, width=10).grid(row=4, column=1, sticky=tk.W, pady=2)
        
    def setup_inference_2d_to_3d_params(self):
        """Configuration pour convertir_inference_2d_to_3d.py"""
        ttk.Label(self.params_frame, text="Dossier images:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.input_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_directory(self.input_path)).grid(row=0, column=2, pady=2)
        
        ttk.Label(self.params_frame, text="Dossier sortie:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.output_path, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_directory(self.output_path)).grid(row=1, column=2, pady=2)
        
        ttk.Label(self.params_frame, text="Slices par volume:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.slices_per_volume = tk.StringVar(value="30")
        ttk.Entry(self.params_frame, textvariable=self.slices_per_volume, width=10).grid(row=2, column=1, sticky=tk.W, pady=2)
        
    def setup_transpose_params(self):
        """Configuration pour transpose_nifti.py"""
        ttk.Label(self.params_frame, text="Fichier d'entrée:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.input_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_file(self.input_path)).grid(row=0, column=2, pady=2)
        
        ttk.Label(self.params_frame, text="Fichier de sortie:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.output_path, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_file_save(self.output_path)).grid(row=1, column=2, pady=2)
        
    def setup_visualise_masks_params(self):
        """Configuration pour visualise_10_mask.py"""
        ttk.Label(self.params_frame, text="Dossier masques:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.input_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(self.params_frame, text="Parcourir", command=lambda: self.select_directory(self.input_path)).grid(row=0, column=2, pady=2)
        
        ttk.Label(self.params_frame, text="Nombre d'images:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.nb_images = tk.StringVar(value="10")
        ttk.Entry(self.params_frame, textvariable=self.nb_images, width=10).grid(row=1, column=1, sticky=tk.W, pady=2)
        
    def select_file(self, var):
        """Ouvre un dialogue de sélection de fichier"""
        filename = filedialog.askopenfilename()
        if filename:
            var.set(filename)
            
    def select_file_save(self, var):
        """Ouvre un dialogue de sauvegarde de fichier"""
        filename = filedialog.asksaveasfilename()
        if filename:
            var.set(filename)
            
    def select_directory(self, var):
        """Ouvre un dialogue de sélection de dossier"""
        dirname = filedialog.askdirectory()
        if dirname:
            var.set(dirname)

    def execute_script(self):
        """Exécute le script sélectionné"""
        script_name = self.script_var.get()
        if not script_name:
            messagebox.showerror("Erreur", "Veuillez sélectionner un script")
            return

        # Effacer la sortie précédente
        self.output_text.delete(1.0, tk.END)
        self.output_queue.put(f"Exécution de {script_name}...\n\n")

        # Désactiver le bouton pendant l'exécution
        self.execute_btn.config(state='disabled')

        # Exécuter dans un thread séparé
        thread = threading.Thread(target=self._run_script_thread, args=(script_name,))
        thread.daemon = True
        thread.start()

    def _run_script_thread(self, script_name):
        """Exécute le script dans un thread séparé"""
        try:
            if "check_shape" in script_name:
                self._run_check_shape()
            elif "dataset_2d_to_3d" in script_name:
                self._run_dataset_2d_to_3d()
            elif "inference_2d_to_3d" in script_name:
                self._run_inference_2d_to_3d()
            elif "transpose_nifti" in script_name:
                self._run_transpose()
            elif "view_nifti_volume" in script_name:
                self._run_view_volume()
            elif "visualise_10_mask" in script_name:
                self._run_visualise_masks()
            elif "visualize_labels" in script_name:
                self._run_analyze_labels()
            elif "visualize_mask" in script_name:
                self._run_visualize_mask()
        except Exception as e:
            self.output_queue.put(f"[ERREUR] {str(e)}\n")
        finally:
            # Réactiver le bouton
            self.root.after(0, lambda: self.execute_btn.config(state='normal'))

    def run_script_simple(self, script_name, script_args):
        """Exécute un script de manière simple"""
        try:
            # Créer la commande
            cmd = [sys.executable, script_name] + script_args

            # Environnement avec encodage UTF-8
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # Exécuter le script
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=env,
                encoding='utf-8',
                errors='replace',
                timeout=300
            )

            # Afficher la sortie
            if result.stdout:
                self.output_queue.put(result.stdout)
            if result.stderr:
                self.output_queue.put(f"[STDERR] {result.stderr}")

            if result.returncode == 0:
                self.output_queue.put("\n[SUCCES] Script exécuté avec succès!\n")
            else:
                self.output_queue.put(f"\n[ERREUR] Code d'erreur: {result.returncode}\n")

        except subprocess.TimeoutExpired:
            self.output_queue.put("[ERREUR] Timeout (5 minutes)\n")
        except Exception as e:
            self.output_queue.put(f"[ERREUR] {str(e)}\n")

    def _run_check_shape(self):
        """Exécute check_shape.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.output_queue.put("[ERREUR] Veuillez sélectionner un fichier\n")
            return

        # Créer un script temporaire simple
        temp_script = f'''import nibabel as nib
try:
    nifti_img = nib.load(r"{file_path}")
    shape = nifti_img.shape
    print("Fichier: {file_path}")
    print("Forme:", shape)
    print("Dimensions:", len(shape))
except Exception as e:
    print("Erreur:", str(e))
'''
        with open("temp_check.py", "w", encoding="utf-8") as f:
            f.write(temp_script)

        self.run_script_simple("temp_check.py", [])

        try:
            os.remove("temp_check.py")
        except:
            pass

    def _run_analyze_labels(self):
        """Exécute visualize_labels.py de manière simple"""
        labels_dir = self.input_path.get()
        if not labels_dir:
            self.output_queue.put("[ERREUR] Veuillez sélectionner un dossier\n")
            return

        # Créer un script temporaire simple
        temp_script = f'''import os
import cv2
import numpy as np
from pathlib import Path

def analyze_label(label_path):
    label = cv2.imread(str(label_path), cv2.IMREAD_GRAYSCALE)
    if label is None:
        print(f"[ERREUR] Impossible de charger: {{label_path.name}}")
        return
    unique_values = np.unique(label)
    print(f"{{label_path.name}}: {{unique_values}}")

labels_dir = Path(r"{labels_dir}")
label_files = list(labels_dir.glob("*.png"))

if not label_files:
    print("[ERREUR] Aucun fichier .png trouvé")
else:
    print(f"[INFO] {{len(label_files)}} fichiers trouvés")
    print("Valeurs uniques dans chaque image:")
    print("-" * 50)
    for label_file in label_files:
        analyze_label(label_file)
'''
        with open("temp_analyze.py", "w", encoding="utf-8") as f:
            f.write(temp_script)

        self.run_script_simple("temp_analyze.py", [])

        try:
            os.remove("temp_analyze.py")
        except:
            pass

    def _run_dataset_2d_to_3d(self):
        """Exécute la conversion dataset 2D vers 3D"""
        self.output_queue.put("[INFO] Fonction non implémentée dans cette version simplifiée\n")
        self.output_queue.put("[INFO] Utilisez le script original directement\n")

    def _run_inference_2d_to_3d(self):
        """Exécute la conversion inférence 2D vers 3D"""
        self.output_queue.put("[INFO] Fonction non implémentée dans cette version simplifiée\n")
        self.output_queue.put("[INFO] Utilisez le script original directement\n")

    def _run_transpose(self):
        """Exécute transpose_nifti.py"""
        input_path = self.input_path.get()
        output_path = self.output_path.get()

        if not input_path or not output_path:
            self.output_queue.put("[ERREUR] Veuillez remplir tous les champs\n")
            return

        temp_script = f'''import nibabel as nib
import numpy as np

try:
    nifti_img = nib.load(r"{input_path}")
    data = nifti_img.get_fdata()
    affine = nifti_img.affine
    transposed_data = np.transpose(data, (1, 2, 0))
    new_nifti = nib.Nifti1Image(transposed_data, affine)
    nib.save(new_nifti, r"{output_path}")
    print("Forme originale:", data.shape)
    print("Forme transposée:", transposed_data.shape)
    print("Sauvegardé dans:", r"{output_path}")
except Exception as e:
    print("Erreur:", str(e))
'''
        with open("temp_transpose.py", "w", encoding="utf-8") as f:
            f.write(temp_script)

        self.run_script_simple("temp_transpose.py", [])

        try:
            os.remove("temp_transpose.py")
        except:
            pass

    def _run_view_volume(self):
        """Exécute view_nifti_volume.py"""
        self.output_queue.put("[INFO] Ouverture du visualiseur interactif...\n")
        self.output_queue.put("[INFO] Contrôles disponibles :\n")
        self.output_queue.put("  ← → : Slice précédente/suivante\n")
        self.output_queue.put("  Page Up/Down : ±10 slices\n")
        self.output_queue.put("  Home/End : Première/dernière slice\n")
        self.output_queue.put("  Q ou Escape : Quitter\n")
        self.output_queue.put("  S : Sauvegarder la slice actuelle\n")
        self.output_queue.put("  I : Informations sur la slice\n")
        self.output_queue.put("[INFO] Détection automatique : masque ou volume d'intensité\n")
        self.output_queue.put("[INFO] Fermez la fenêtre pour continuer\n\n")

        file_path = self.file_path.get()
        if not file_path:
            self.output_queue.put("[ERREUR] Veuillez sélectionner un fichier\n")
            return

        # Créer un script temporaire avec le nouveau visualiseur
        temp_script = f'''
import sys
sys.path.append(r"{os.getcwd()}")

from view_nifti_volume import VolumeViewer

try:
    print("Lancement du visualiseur...")
    viewer = VolumeViewer(r"{file_path}")
    print("Visualiseur fermé.")
except Exception as e:
    print(f"Erreur: {{e}}")
'''

        with open("temp_view.py", "w", encoding="utf-8") as f:
            f.write(temp_script)

        self.run_script_simple("temp_view.py", [])

        try:
            os.remove("temp_view.py")
        except:
            pass

    def _run_visualise_masks(self):
        """Exécute visualise_10_mask.py"""
        self.output_queue.put("[INFO] Fonction non implémentée dans cette version simplifiée\n")

    def _run_visualize_mask(self):
        """Exécute visualize_mask.py"""
        self.output_queue.put("[INFO] Fonction non implémentée dans cette version simplifiée\n")


def main():
    """Fonction principale"""
    root = tk.Tk()
    app = SimpleScriptGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
