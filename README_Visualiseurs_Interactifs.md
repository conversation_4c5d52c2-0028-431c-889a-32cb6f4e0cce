# 🎨 Visualiseurs Interactifs - Guide Complet

Deux nouveaux visualiseurs interactifs ont été créés pour remplacer les anciens scripts automatiques par des interfaces contrôlables manuellement.

## 🆕 Nouveaux Visualiseurs

### 1. 📊 **Visualiseur de Volumes NIfTI** (`view_nifti_volume.py`)
- **Remplace** : L'ancien script automatique
- **Fonction** : Visualisation interactive de volumes 3D .nii.gz

### 2. 🖼️ **Visualiseur de Dossiers PNG** (`view_png_folder.py`)
- **Remplace** : `visualise_10_mask.py` (supprimé)
- **Fonction** : Visualisation interactive d'images PNG dans un dossier

## ✨ Fonctionnalités Communes

### 🎯 **Détection Automatique Intelligente**

Les deux visualiseurs détectent automatiquement le type de données :

**🎨 Masque de Segmentation** (valeurs 0, 1, 2, 3, 4) :
- **Colormap automatique** avec couleurs spécifiques
- **Légende des classes** avec noms
- **Statistiques par classe**

**📊 Volume/Image d'Intensité** (valeurs continues) :
- **Niveaux de gris** automatiques
- **Normalisation** adaptative
- **Statistiques min/max**

### 🎮 **Contrôles Interactifs Identiques**

| Touche | Action |
|--------|--------|
| `← →` | Précédent/Suivant |
| `↑ ↓` | Précédent/Suivant |
| `Page Up/Down` | ±10 éléments |
| `Home` | Premier élément |
| `End` | Dernier élément |
| `Q` ou `Escape` | Quitter |
| `S` | Sauvegarder avec colormap |
| `I` | Informations détaillées |

### 🎨 **Colormap pour Masques**

Quand des valeurs 0-4 sont détectées :
- **0 - Background** : Noir
- **1 - Frontwall** : Bleu
- **2 - Backwall** : Vert
- **3 - Flaw** : Rouge
- **4 - Indication** : Jaune

## 🚀 Utilisation

### 📱 **Via l'Interface Graphique** (Recommandé)

```bash
python interface_simple.py
```

1. **Pour volumes NIfTI** :
   - Sélectionnez "Visualiser un volume NIfTI"
   - Choisissez votre fichier .nii.gz

2. **Pour dossiers PNG** :
   - Sélectionnez "Visualiser dossier PNG"
   - Choisissez votre dossier
   - Optionnel : Limitez le nombre d'images

### 💻 **Directement en Script**

```bash
# Volume NIfTI
python view_nifti_volume.py

# Dossier PNG
python view_png_folder.py
```

### 🐍 **Import dans vos Scripts**

```python
# Volume NIfTI
from view_nifti_volume import VolumeViewer
viewer = VolumeViewer("mon_volume.nii.gz")

# Dossier PNG
from view_png_folder import PNGFolderViewer
viewer = PNGFolderViewer("mon_dossier/", max_images=20)
```

## 🧪 Scripts de Test

### 🔬 **Test des Volumes NIfTI**
```bash
python test_viewer.py
```
Crée `test_mask.nii.gz` et `test_intensity.nii.gz`

### 🖼️ **Test des Dossiers PNG**
```bash
python test_png_viewer.py
```
Crée `test_png_masks/` et `test_png_intensity/`

## 📊 Exemples de Détection

### ✅ **Masque Détecté**
```
Valeurs uniques : [0 1 2 3 4]
Détection : Images de masque/segmentation détectées
Application du colormap pour les classes 0-4
```

### ✅ **Intensité Détectée**
```
Valeurs uniques : [0.0 0.1 ... 255.9 256.0]
Détection : Images d'intensité standard détectées
Application du colormap en niveaux de gris
```

## 🔍 Informations Affichées

### 📋 **Dans l'Interface**
- **Numéro** : Élément actuel / total
- **Classes présentes** (masques) ou **Min/Max** (intensité)
- **Instructions** de contrôle en permanence

### 📊 **Avec la Touche `I`**
```
=== INFORMATIONS IMAGE 5 ===
Fichier : mask_004.png
Forme : (100, 100)
Type : Masque de segmentation
Min/Max : 0 / 4
Valeurs uniques et leurs occurrences :
  Background (0) : 8500 pixels (85.0%)
  Frontwall (1) : 500 pixels (5.0%)
  Flaw (3) : 800 pixels (8.0%)
  Indication (4) : 200 pixels (2.0%)
==================================================
```

## 💾 Sauvegarde avec Colormap

Appuyez sur `S` pour sauvegarder :
- **Volume NIfTI** : `slice_XXX.png`
- **Dossier PNG** : `colored_nom_original.png`
- **Format** : PNG haute résolution (300 DPI)
- **Inclut** : Colormap appliquée et titre

## ⚡ Avantages vs Anciens Scripts

| Aspect | Ancien | Nouveau |
|--------|--------|---------|
| **Navigation** | Automatique | Contrôle manuel |
| **Détection** | Manuelle | Automatique |
| **Colormap** | Fixe | Adaptatif |
| **Informations** | Limitées | Détaillées |
| **Sauvegarde** | Non | Oui |
| **Contrôles** | Aucun | Complets |

## 🔧 Configuration Interface Graphique

L'interface simplifiée a été mise à jour :

- ✅ **"Visualiser dossier PNG"** remplace "Visualiser 10 masques"
- ✅ **Paramètre optionnel** : Nombre max d'images
- ✅ **Instructions claires** affichées avant lancement
- ✅ **Gestion d'erreurs** robuste

## 🐛 Résolution de Problèmes

### **Problème** : Mauvaise détection du type
**Solution** : Le fallback en niveaux de gris fonctionne toujours

### **Problème** : Trop d'images dans le dossier
**Solution** : Utilisez le paramètre `max_images` dans l'interface

### **Problème** : Contrôles ne répondent pas
**Solution** : Cliquez sur la fenêtre matplotlib pour lui donner le focus

## 🎯 Cas d'Usage Typiques

### 🔬 **Analyse de Résultats de Segmentation**
1. Lancez le visualiseur sur vos résultats
2. Naviguez avec `← →` pour voir l'évolution
3. Utilisez `I` pour analyser les statistiques
4. Sauvegardez les slices intéressantes avec `S`

### 📊 **Comparaison de Datasets**
1. Visualisez le dossier d'images originales
2. Puis le dossier de masques correspondants
3. Comparez slice par slice
4. Analysez les distributions avec `I`

### 🎨 **Préparation de Figures**
1. Naviguez jusqu'aux slices représentatives
2. Sauvegardez avec `S` (colormap appliquée)
3. Utilisez les images sauvegardées dans vos publications

---

**🎉 Profitez de vos nouveaux visualiseurs interactifs avec contrôle total !**
