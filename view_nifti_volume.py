import nibabel as nib
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

# === Chemin vers le fichier de masque segmenté .nii.gz ===
path_to_seg = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent_3d_v3\case_001.nii.gz"

# === Chargement ===
seg = nib.load(path_to_seg).get_fdata().astype(np.uint8)
print(f"✅ Volume chargé : {seg.shape} (Z, Y, X)")

# === Définition du colormap (index = classe) ===
# 0: noir, 1: bleu, 2: vert, 3: rouge, 4: jaune
cmap = ListedColormap([
    (0, 0, 0),        # background - noir
    (0, 0, 1),        # 1 - bleu
    (0, 1, 0),        # 2 - vert
    (1, 0, 0),        # 3 - rouge
    (1, 1, 0)         # 4 - jaune
])

# === Affichage des slices ===
for i in range(seg.shape[0]):
    plt.imshow(seg[i, :, :], cmap=cmap, vmin=0, vmax=4)
    plt.title(f"Slice {i+1}/{seg.shape[0]}")
    plt.axis('off')
    plt.pause(0.1)

plt.show()
