# 🔬 Interfaces Graphiques de Traitement d'Images Médicales

Deux interfaces graphiques sont disponibles pour utiliser vos scripts de traitement d'images médicales.

## 🚀 Interface Simplifiée (RECOMMANDÉE)

**Fichier**: `interface_simple.py`

### ✅ Avantages :
- **Stable et robuste** - Pas de problèmes de récursion
- **Communication thread-safe** avec queue
- **Gestion d'erreurs améliorée**
- **Scripts intégrés** pour les fonctions de base

### 🎯 Fonctions disponibles :
- ✅ **Vérifier la forme d'un fichier NIfTI** - Fonctionne parfaitement
- ✅ **Analyser les labels** - Fonctionne parfaitement  
- ✅ **Transposer un fichier NIfTI** - Fonctionne parfaitement
- ✅ **Visualiser un volume NIfTI** - Fonctionne parfaitement
- ⚠️ **Convertir dataset 2D vers 3D** - Redirige vers script original
- ⚠️ **Convertir inférence 2D vers 3D** - Redirige vers script original
- ⚠️ **Visualiser masques** - Redirige vers script original

### 🚀 Lancement :
```bash
python interface_simple.py
```

## 🔧 Interface Complète (EXPÉRIMENTALE)

**Fichier**: `interface_graphique.py`

### ⚠️ Problèmes connus :
- **Erreurs de récursion** possibles avec certains scripts
- **Problèmes d'encodage** sur Windows
- **Interface peut se bloquer** lors de l'exécution de scripts longs

### ✨ Avantages :
- **Toutes les fonctions** disponibles
- **Scripts temporaires** générés automatiquement
- **Interface plus complète**

## 📋 Guide d'Utilisation (Interface Simplifiée)

### 1. **Vérifier la forme d'un fichier NIfTI**
- Sélectionnez "Vérifier la forme d'un fichier NIfTI"
- Cliquez sur "Parcourir" pour sélectionner votre fichier .nii.gz
- Cliquez sur "Exécuter"
- Résultat : Affiche les dimensions du volume

### 2. **Analyser les labels**
- Sélectionnez "Analyser les labels"
- Cliquez sur "Parcourir" pour sélectionner le dossier contenant vos labels PNG
- Cliquez sur "Exécuter"
- Résultat : Liste les valeurs uniques dans chaque image de label

### 3. **Transposer un fichier NIfTI**
- Sélectionnez "Transposer un fichier NIfTI"
- Sélectionnez le fichier d'entrée (.nii.gz)
- Choisissez le nom/chemin du fichier de sortie
- Cliquez sur "Exécuter"
- Résultat : Crée un nouveau fichier avec les axes transposés

### 4. **Visualiser un volume NIfTI**
- Sélectionnez "Visualiser un volume NIfTI"
- Sélectionnez votre fichier de segmentation .nii.gz
- Cliquez sur "Exécuter"
- Résultat : Ouvre une fenêtre matplotlib avec les slices colorées

## 🔧 Architecture Technique

### Interface Simplifiée :
- **Queue-based communication** : Évite les problèmes de thread
- **Scripts temporaires simples** : Code Python minimal
- **Gestion d'erreurs robuste** : Try/catch à tous les niveaux
- **Timeout de 5 minutes** : Évite les blocages

### Différences clés :
```python
# Interface complète (problématique)
self.root.after(0, lambda: self._append_output_main_thread(text))

# Interface simplifiée (robuste)
self.output_queue.put(message)
# + check_queue() périodique
```

## 🐛 Résolution des Problèmes

### Problème : "RecursionError: maximum recursion depth exceeded"
**Solution** : Utilisez `interface_simple.py` au lieu de `interface_graphique.py`

### Problème : "UnicodeEncodeError" 
**Solution** : L'interface simplifiée gère automatiquement l'encodage UTF-8

### Problème : Interface qui se bloque
**Solution** : L'interface simplifiée utilise des timeouts et une communication par queue

## 📝 Recommandations

1. **Utilisez `interface_simple.py`** pour un usage quotidien
2. **Pour les conversions complexes**, utilisez directement les scripts originaux :
   - `convertir_dataset_2d_to_3d.py`
   - `convertir_inference_2d_to_3d.py`
3. **Testez toujours** avec un petit échantillon de données d'abord
4. **Fermez les fenêtres matplotlib** après visualisation pour libérer la mémoire

## 🎯 Prochaines Améliorations

- [ ] Intégration complète des scripts de conversion dans l'interface simplifiée
- [ ] Barre de progression pour les opérations longues
- [ ] Sauvegarde des paramètres utilisés
- [ ] Historique des exécutions
- [ ] Validation avancée des paramètres d'entrée

---

**Recommandation finale** : Utilisez `interface_simple.py` pour une expérience stable et fiable !
