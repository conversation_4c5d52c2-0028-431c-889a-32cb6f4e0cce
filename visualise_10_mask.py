import os
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image

# === Paramètres ===
MASKS_DIR = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent_uint8"  # 📂 Chemin vers les masques
NB_IMAGES = 10

# === Palette personnalisée ===
# Format: [R, G, B]
colors = {
    0: [0, 0, 0],        # Fond → noir
    1: [0, 0, 255],      # Classe 1 → bleu
    2: [0, 255, 0],      # Classe 2 → vert
    3: [255, 0, 0],      # Classe 3 → rouge
    4: [255, 255, 0],    # Classe 4 → jaune
}

def apply_color_map(mask):
    """Transforme un masque de classes en image RGB colorée."""
    h, w = mask.shape
    color_mask = np.zeros((h, w, 3), dtype=np.uint8)
    for cls, rgb in colors.items():
        color_mask[mask == cls] = rgb
    return color_mask

# === Lecture et affichage ===
all_files = sorted([f for f in os.listdir(MASKS_DIR) if f.endswith(".png")])[:NB_IMAGES]

plt.figure(figsize=(20, 10))
for i, fname in enumerate(all_files):
    mask_path = os.path.join(MASKS_DIR, fname)
    mask = np.array(Image.open(mask_path).convert("L"))

    color_mask = apply_color_map(mask)

    plt.subplot(2, 5, i + 1)
    plt.imshow(color_mask)
    plt.title(fname)
    plt.axis("off")

plt.tight_layout()
plt.show()
