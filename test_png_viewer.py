"""
Script de test pour le visualiseur PNG
Crée des images de test pour tester le nouveau visualiseur
"""

import numpy as np
from PIL import Image
import os
from view_png_folder import PNGFolderViewer

def create_test_png_masks():
    """Crée un dossier avec des masques PNG de test"""
    test_folder = "test_png_masks"
    os.makedirs(test_folder, exist_ok=True)
    
    print(f"Création de masques PNG de test dans {test_folder}/")
    
    # Créer 15 images de masques avec différentes classes
    for i in range(15):
        # Image 100x100
        mask = np.zeros((100, 100), dtype=np.uint8)
        
        # Background (0) - déjà défini
        
        # Frontwall (1) - ligne horizontale qui bouge
        y_pos = 10 + i
        mask[y_pos:y_pos+5, :] = 1
        
        # Backwall (2) - ligne horizontale en bas
        mask[85:90, :] = 2
        
        # Flaw (3) - cercle qui grandit
        center_y, center_x = 50, 50
        radius = 5 + i
        y, x = np.ogrid[:100, :100]
        circle_mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
        mask[circle_mask] = 3
        
        # Indication (4) - points aléatoires
        np.random.seed(i)
        for _ in range(3):
            y_pos = np.random.randint(20, 80)
            x_pos = np.random.randint(20, 80)
            mask[y_pos:y_pos+2, x_pos:x_pos+2] = 4
        
        # Sauvegarder l'image
        filename = f"mask_{i:03d}.png"
        filepath = os.path.join(test_folder, filename)
        Image.fromarray(mask).save(filepath)
    
    print(f"✅ {15} masques créés avec les valeurs 0-4")
    return test_folder

def create_test_png_intensity():
    """Crée un dossier avec des images d'intensité de test"""
    test_folder = "test_png_intensity"
    os.makedirs(test_folder, exist_ok=True)
    
    print(f"Création d'images d'intensité de test dans {test_folder}/")
    
    # Créer 10 images d'intensité
    for i in range(10):
        # Image 80x80 avec valeurs continues
        image = np.random.normal(128, 30, (80, 80))
        
        # Ajouter des structures
        # Gradient
        for x in range(80):
            image[:, x] += x * 1.5
        
        # Structure circulaire qui bouge
        center_y, center_x = 40, 20 + i * 4
        radius = 15
        y, x = np.ogrid[:80, :80]
        circle_mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
        image[circle_mask] += 50
        
        # Normaliser entre 0 et 255
        image = np.clip(image, 0, 255).astype(np.uint8)
        
        # Sauvegarder l'image
        filename = f"intensity_{i:03d}.png"
        filepath = os.path.join(test_folder, filename)
        Image.fromarray(image).save(filepath)
    
    print(f"✅ {10} images d'intensité créées")
    return test_folder

def test_png_viewer():
    """Teste le visualiseur PNG avec différents types d'images"""
    print("=== TEST DU VISUALISEUR PNG ===\n")
    
    # Test 1 : Masques de segmentation
    print("1. Test avec des masques de segmentation (valeurs 0-4)")
    mask_folder = create_test_png_masks()
    print("Ouverture du visualiseur pour les masques...")
    print("Utilisez les contrôles clavier pour naviguer")
    print("Fermez la fenêtre pour continuer...\n")
    
    try:
        viewer1 = PNGFolderViewer(mask_folder)
    except Exception as e:
        print(f"Erreur avec les masques : {e}")
    
    # Test 2 : Images d'intensité
    print("\n2. Test avec des images d'intensité")
    intensity_folder = create_test_png_intensity()
    print("Ouverture du visualiseur pour les images d'intensité...")
    print("Fermez la fenêtre pour continuer...\n")
    
    try:
        viewer2 = PNGFolderViewer(intensity_folder)
    except Exception as e:
        print(f"Erreur avec les images d'intensité : {e}")
    
    # Test 3 : Limitation du nombre d'images
    print("\n3. Test avec limitation à 5 images")
    print("Ouverture du visualiseur avec max 5 images...")
    
    try:
        viewer3 = PNGFolderViewer(mask_folder, max_images=5)
    except Exception as e:
        print(f"Erreur avec la limitation : {e}")
    
    print("\n=== TESTS TERMINÉS ===")
    print("Les dossiers de test ont été créés :")
    print(f"- {mask_folder}/ (masques de segmentation)")
    print(f"- {intensity_folder}/ (images d'intensité)")
    print("Vous pouvez les utiliser pour tester l'interface graphique.")
    
    print("\n=== CONTRÔLES DISPONIBLES ===")
    print("← → : Image précédente/suivante")
    print("Page Up/Down : ±10 images")
    print("Home/End : Première/dernière image")
    print("Q ou Escape : Quitter")
    print("S : Sauvegarder l'image actuelle avec colormap")
    print("I : Informations détaillées sur l'image")

if __name__ == "__main__":
    test_png_viewer()
